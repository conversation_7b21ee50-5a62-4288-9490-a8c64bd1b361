import {
    geometry, gfx,
    renderer, rendering,
    Vec3
} from 'cc';
import {
    PipelineConfigs
} from './pipeline-utils';

// Additional imports

// Forward declaration - will be imported from builtin-pipeline
export interface CameraConfigs {
    settings: any;
    isMainGameWindow: boolean;
    enableShadingScale: boolean;
    radianceFormat: gfx.Format;
    width: number;
    height: number;
    remainingPasses: number;
}

const { Sphere, intersect } = geometry;
const { ClearFlagBit, Color, Format, LoadOp, StoreOp, Viewport } = gfx;
const { scene } = renderer;
const { CameraUsage, CSMLevel, LightType } = scene;

class ForwardLighting {
    // Active lights
    private readonly lights: renderer.scene.Light[] = [];
    // Active spot lights with shadows (Mutually exclusive with `lights`)
    private readonly shadowEnabledSpotLights: renderer.scene.SpotLight[] = [];

    // Internal cached resources
    private readonly _sphere = Sphere.create(0, 0, 0, 1);
    private readonly _boundingBox = new geometry.AABB();
    private readonly _rangedDirLightBoundingBox = new geometry.AABB(0.0, 0.0, 0.0, 0.5, 0.5, 0.5);

    // ----------------------------------------------------------------
    // Interface
    // ----------------------------------------------------------------
    public cullLights(scene: renderer.RenderScene, frustum: geometry.Frustum, cameraPos?: Vec3): void {
        // TODO(zhouzhenglong): Make light culling native
        this.lights.length = 0;
        this.shadowEnabledSpotLights.length = 0;
        // spot lights
        for (const light of scene.spotLights) {
            if (light.baked) {
                continue;
            }
            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);
            if (intersect.sphereFrustum(this._sphere, frustum)) {
                if (light.shadowEnabled) {
                    this.shadowEnabledSpotLights.push(light);
                } else {
                    this.lights.push(light);
                }
            }
        }
        // sphere lights
        for (const light of scene.sphereLights) {
            if (light.baked) {
                continue;
            }
            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);
            if (intersect.sphereFrustum(this._sphere, frustum)) {
                this.lights.push(light);
            }
        }
        // point lights
        for (const light of scene.pointLights) {
            if (light.baked) {
                continue;
            }
            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);
            if (intersect.sphereFrustum(this._sphere, frustum)) {
                this.lights.push(light);
            }
        }
        // ranged dir lights
        for (const light of scene.rangedDirLights) {
            geometry.AABB.transform(this._boundingBox, this._rangedDirLightBoundingBox, light.node!.getWorldMatrix());
            if (intersect.aabbFrustum(this._boundingBox, frustum)) {
                this.lights.push(light);
            }
        }

        if (cameraPos) {
            this.shadowEnabledSpotLights.sort(
                (lhs, rhs) => Vec3.squaredDistance(cameraPos, lhs.position) - Vec3.squaredDistance(cameraPos, rhs.position),
            );
        }
    }
    private _addLightQueues(camera: renderer.scene.Camera, pass: rendering.BasicRenderPassBuilder): void {
        for (const light of this.lights) {
            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');
            switch (light.type) {
                case LightType.SPHERE:
                    queue.name = 'sphere-light';
                    break;
                case LightType.SPOT:
                    queue.name = 'spot-light';
                    break;
                case LightType.POINT:
                    queue.name = 'point-light';
                    break;
                case LightType.RANGED_DIRECTIONAL:
                    queue.name = 'ranged-directional-light';
                    break;
                default:
                    queue.name = 'unknown-light';
            }
            queue.addScene(
                camera,
                rendering.SceneFlags.BLEND,
                light,
            );
        }
    }
    public addSpotlightShadowPasses(
        ppl: rendering.BasicPipeline,
        camera: renderer.scene.Camera,
        maxNumShadowMaps: number,
    ): void {
        let i = 0;
        for (const light of this.shadowEnabledSpotLights) {
            const shadowMapSize = ppl.pipelineSceneData.shadows.size;
            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');
            shadowPass.name = `SpotLightShadowPass${i}`;
            shadowPass.addRenderTarget(`SpotShadowMap${i}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));
            shadowPass.addDepthStencil(`SpotShadowDepth${i}`, LoadOp.CLEAR, StoreOp.DISCARD);
            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')
                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)
                .useLightFrustum(light);
            ++i;
            if (i >= maxNumShadowMaps) {
                break;
            }
        }
    }
    public addLightQueues(pass: rendering.BasicRenderPassBuilder,
        camera: renderer.scene.Camera, maxNumShadowMaps: number): void {
        this._addLightQueues(camera, pass);
        let i = 0;
        for (const light of this.shadowEnabledSpotLights) {
            // Add spot-light pass
            // Save last RenderPass to the `pass` variable
            // TODO(zhouzhenglong): Fix per queue addTexture
            pass.addTexture(`SpotShadowMap${i}`, 'cc_spotShadowMap');
            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');
            queue.addScene(camera, rendering.SceneFlags.BLEND, light);
            ++i;
            if (i >= maxNumShadowMaps) {
                break;
            }
        }
    }

    // Notice: ForwardLighting cannot handle a lot of lights.
    // If there are too many lights, the performance will be very poor.
    // If many lights are needed, please implement a forward+ or deferred rendering pipeline.
    public addLightPasses(
        colorName: string,
        depthStencilName: string,
        depthStencilStoreOp: gfx.StoreOp,
        id: number, // window id
        width: number,
        height: number,
        camera: renderer.scene.Camera,
        viewport: gfx.Viewport,
        ppl: rendering.BasicPipeline,
        pass: rendering.BasicRenderPassBuilder,
    ): rendering.BasicRenderPassBuilder {
        this._addLightQueues(camera, pass);

        let count = 0;
        for (const light of this.shadowEnabledSpotLights) {
            // Add spot-light pass
            // Save last RenderPass to the `pass` variable
            ++count;
            const storeOp = count === this.shadowEnabledSpotLights.length
                ? depthStencilStoreOp
                : StoreOp.STORE;

            pass = ppl.addRenderPass(width, height, 'default');
            pass.name = 'SpotlightWithShadowMap';
            pass.setViewport(viewport);
            pass.addRenderTarget(colorName, LoadOp.LOAD);
            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, storeOp);
            pass.addTexture(`ShadowMap${id}`, 'cc_spotShadowMap');
            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');
            queue.addScene(
                camera,
                rendering.SceneFlags.BLEND,
                light,
            );
        }
        return pass;
    }

    public isMultipleLightPassesNeeded(): boolean {
        return this.shadowEnabledSpotLights.length > 0;
    }
}

export interface ForwardPassConfigs {
    enableMainLightShadowMap: boolean;
    enableMainLightPlanarShadowMap: boolean;
    enablePlanarReflectionProbe: boolean;
    enableMSAA: boolean;
    enableSingleForwardPass: boolean;
}

export class BuiltinForwardPassBuilder implements rendering.PipelinePassBuilder {
    static ConfigOrder = 100;
    static RenderOrder = 100;
    getConfigOrder(): number {
        return BuiltinForwardPassBuilder.ConfigOrder;
    }
    getRenderOrder(): number {
        return BuiltinForwardPassBuilder.RenderOrder;
    }
    configCamera(
        camera: Readonly<renderer.scene.Camera>,
        pipelineConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ForwardPassConfigs): void {
        // Shadow
        cameraConfigs.enableMainLightShadowMap = pipelineConfigs.shadowEnabled
            && !pipelineConfigs.usePlanarShadow
            && !!camera.scene
            && !!camera.scene.mainLight
            && camera.scene.mainLight.shadowEnabled;

        cameraConfigs.enableMainLightPlanarShadowMap = pipelineConfigs.shadowEnabled
            && pipelineConfigs.usePlanarShadow
            && !!camera.scene
            && !!camera.scene.mainLight
            && camera.scene.mainLight.shadowEnabled;

        // Reflection Probe
        cameraConfigs.enablePlanarReflectionProbe = cameraConfigs.isMainGameWindow
            || camera.cameraUsage === CameraUsage.SCENE_VIEW
            || camera.cameraUsage === CameraUsage.GAME_VIEW;

        // MSAA
        cameraConfigs.enableMSAA = cameraConfigs.settings.msaa.enabled
            && !cameraConfigs.enableShadingScale;

        // Forward pass
        cameraConfigs.enableSingleForwardPass = cameraConfigs.enableMSAA
            || pipelineConfigs.isMobile;

        ++cameraConfigs.remainingPasses;
    }
    windowResize(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ForwardPassConfigs,
        window: renderer.RenderWindow): void {
        const id = window.renderWindowId;
        const width = cameraConfigs.width;
        const height = cameraConfigs.height;

        // Main light shadow map
        if (cameraConfigs.enableMainLightShadowMap) {
            const shadowSize = ppl.pipelineSceneData.shadows.size;
            ppl.addRenderTarget(`ShadowMap${id}`, pplConfigs.shadowMapFormat, shadowSize.x, shadowSize.y);
            ppl.addDepthStencil(`ShadowDepth${id}`, Format.DEPTH_STENCIL, shadowSize.x, shadowSize.y);
        }

        // Spot light shadow maps
        if (cameraConfigs.enableSingleForwardPass) {
            const shadowMapSize = ppl.pipelineSceneData.shadows.size;
            for (let i = 0; i !== pplConfigs.mobileMaxSpotLightShadowMaps; ++i) {
                ppl.addRenderTarget(`SpotShadowMap${i}`, pplConfigs.shadowMapFormat, shadowMapSize.x, shadowMapSize.y);
                ppl.addDepthStencil(`SpotShadowDepth${i}`, Format.DEPTH_STENCIL, shadowMapSize.x, shadowMapSize.y);
            }
        }

        // Forward pass
        if (cameraConfigs.enableMSAA) {
            const sampleCount = cameraConfigs.settings.msaa.sampleCount;
            const { TextureType } = gfx;
            const { ResourceFlags, ResourceResidency } = rendering;
            ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, cameraConfigs.radianceFormat, width, height, 1, 1, 1,
                sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);
            ppl.addTexture(`MsaaDepthStencil${id}`, TextureType.TEX2D, Format.DEPTH_STENCIL, width, height, 1, 1, 1,
                sampleCount, ResourceFlags.DEPTH_STENCIL_ATTACHMENT, ResourceResidency.MEMORYLESS);
        }

        // Reflection probe
        // TODO: Add reflection probe passes
    }

    setup(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ForwardPassConfigs,
        camera: renderer.scene.Camera,
        context: PipelineContext): rendering.BasicRenderPassBuilder | undefined {
        const id = camera.window.renderWindowId;

        const scene = camera.scene!;
        const mainLight = scene.mainLight;

        --cameraConfigs.remainingPasses;
        // assert(cameraConfigs.remainingPasses >= 0);

        // Forward Lighting (Light Culling)
        this.forwardLighting.cullLights(scene, camera.frustum);

        // Main Directional light CSM Shadow Map
        if (cameraConfigs.enableMainLightShadowMap) {
            // assert(!!mainLight);
            this._addCascadedShadowMapPass(ppl, pplConfigs, id, mainLight!, camera);
        }

        // Spot light shadow maps (Mobile or MSAA)
        if (cameraConfigs.enableSingleForwardPass) {
            // Currently, only support 1 spot light with shadow map on mobile platform.
            // TODO(zhouzhenglong): Relex this limitation.
            this.forwardLighting.addSpotlightShadowPasses(
                ppl, camera, pplConfigs.mobileMaxSpotLightShadowMaps);
        }

        // Reflection Probe
        // TODO: Add reflection probe passes

        // Forward Pass
        const pass = this._addForwardRadiancePasses(
            ppl, pplConfigs, cameraConfigs, id, camera,
            cameraConfigs.width, cameraConfigs.height, mainLight,
            context.colorName, context.depthStencilName);

        context.colorName = context.colorName;
        context.depthStencilName = context.depthStencilName;

        return pass;
    }

    private readonly forwardLighting = new ForwardLighting();
    private readonly _viewport = new Viewport();
    private readonly _clearColor = new Color(0, 0, 0, 1);
    private readonly _reflectionProbeClearColor = new Vec3(0, 0, 0);
}
