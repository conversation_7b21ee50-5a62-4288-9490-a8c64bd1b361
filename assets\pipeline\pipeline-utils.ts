import {
    assert,
    Color,
    gfx, Material, renderer, rendering, Vec2,
    Vec4
} from 'cc';
import {
    PipelineSettings
} from './builtin-pipeline-types';

const { scene } = renderer;
const { CSMLevel } = scene;
const { ClearFlagBit } = gfx;

export const sClearColorTransparentBlack = new Color(0, 0, 0, 0);

export function forwardNeedClearColor(camera: renderer.scene.Camera): boolean {
    // 注意：这里使用 gfx.ClearFlagBit 而不是 scene.ClearFlagBit
    return !!(camera.clearFlag & (gfx.ClearFlagBit.COLOR | (gfx.ClearFlagBit.STENCIL << 1)));
}

export function getCsmMainLightViewport(
    light: renderer.scene.DirectionalLight,
    w: number,
    h: number,
    level: number,
    vp: gfx.Viewport,
    screenSpaceSignY: number,
): void {
    if (light.shadowFixedArea || light.csmLevel === CSMLevel.LEVEL_1) {
        vp.left = 0;
        vp.top = 0;
        vp.width = Math.trunc(w);
        vp.height = Math.trunc(h);
    } else {
        vp.left = Math.trunc(level % 2 * 0.5 * w);
        if (screenSpaceSignY > 0) {
            vp.top = Math.trunc((1 - Math.floor(level / 2)) * 0.5 * h);
        } else {
            vp.top = Math.trunc(Math.floor(level / 2) * 0.5 * h);
        }
        vp.width = Math.trunc(0.5 * w);
        vp.height = Math.trunc(0.5 * h);
    }
    vp.left = Math.max(0, vp.left);
    vp.top = Math.max(0, vp.top);
    vp.width = Math.max(1, vp.width);
    vp.height = Math.max(1, vp.height);
}

export function convertToGfxColor(color: Color): gfx.Color {
    // 创建新的 gfx.Color 实例来确保类型兼容
    return new gfx.Color(color.x, color.y, color.z, color.w);
}

export interface PipelineContext {
    colorName: string;
    depthStencilName: string;
}

export interface CameraConfigs {
    settings: PipelineSettings;
    // Window
    isMainGameWindow: boolean;
    renderWindowId: number;
    // Camera
    colorName: string;
    depthStencilName: string;
    // Pipeline
    enableFullPipeline: boolean;
    enableProfiler: boolean;
    remainingPasses: number;
    // Shading Scale
    enableShadingScale: boolean;
    shadingScale: number;
    nativeWidth: number;
    nativeHeight: number;
    width: number; // Scaled width
    height: number; // Scaled height
    // Radiance
    enableHDR: boolean;
    radianceFormat: gfx.Format;
    // Tone Mapping
    copyAndTonemapMaterial: Material | null;
    // Depth
    /** @en mutable */
    enableStoreSceneDepth: boolean;
}

export class PipelineConfigs {
    isWeb = false;
    isWebGL1 = false;
    isWebGPU = false;
    isMobile = false;
    isHDR = false;
    useFloatOutput = false;
    toneMappingType = 0; // 0: ACES, 1: None
    shadowEnabled = false;
    shadowMapFormat = gfx.Format.R32F;
    shadowMapSize = new Vec2(1, 1);
    usePlanarShadow = false;
    screenSpaceSignY = 1;
    supportDepthSample = false;
    mobileMaxSpotLightShadowMaps = 1;

    platform = new Vec4(0, 0, 0, 0);
}

export function addCopyToScreenPass(
    ppl: rendering.BasicPipeline,
    pplConfigs: Readonly<PipelineConfigs>,
    cameraConfigs: CameraConfigs,
    input: string,
): rendering.BasicRenderPassBuilder {
    assert(!!cameraConfigs.copyAndTonemapMaterial);
    const pass = ppl.addRenderPass(
        cameraConfigs.nativeWidth,
        cameraConfigs.nativeHeight,
        'cc-tone-mapping');
    pass.addRenderTarget(
        cameraConfigs.colorName,
        gfx.LoadOp.CLEAR, gfx.StoreOp.STORE,
        convertToGfxColor(sClearColorTransparentBlack));
    pass.addTexture(input, 'inputTexture');
    pass.setVec4('g_platform', pplConfigs.platform);
    pass.addQueue(rendering.QueueHint.OPAQUE)
        .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 1);
    return pass;
}

export function getPingPongRenderTarget(prevName: string, prefix: string, id: number): string {
    if (prevName.startsWith(prefix)) {
        return `${prefix}${1 - Number(prevName.charAt(prefix.length))}_${id}`;
    } else {
        return `${prefix}0_${id}`;
    }
}
